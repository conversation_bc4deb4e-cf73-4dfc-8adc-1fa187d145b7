import { BrowserWindow } from 'electron';
import path from 'node:path';
import { createLogger } from '../../src/libs/logger';

const logger = createLogger('WindowManager');

class WindowManager {
  private mainWindow: BrowserWindow | null = null;
  private floatingWindow: BrowserWindow | null = null;
  private preloadPath: string;
  private viteDevServerUrl: string | undefined;
  private webProdUrl = 'http://172.16.0.117:8950/';

  constructor(preloadPath: string) {
    this.preloadPath = preloadPath;
    this.viteDevServerUrl = process.env['VITE_DEV_SERVER_URL'];
  }

  createMainWindow(): BrowserWindow {
    this.mainWindow = new BrowserWindow({
      show: false,
      webPreferences: {
        preload: this.preloadPath,
        webSecurity: false,
        nodeIntegration: true,
      },
      frame: false,
      width: 1000,
      height: 562.5,
      minWidth: 1000,
      minHeight: 562.5,
      backgroundColor: '#eee',
    });

    if (this.viteDevServerUrl) {
      this.mainWindow.loadURL(this.viteDevServerUrl);
    } else {
      this.mainWindow.loadURL(this.webProdUrl);
    }

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
    });

    this.mainWindow.on('maximize', () => {
      this.mainWindow?.webContents.send('window-state-changed', this.mainWindow.isMaximized());
    });

    this.mainWindow.on('unmaximize', () => {
      this.mainWindow?.webContents.send('window-state-changed', this.mainWindow.isMaximized());
    });

    logger.info('主窗口创建完成');
    return this.mainWindow;
  }

  createFloatingWindow(): BrowserWindow {
    this.floatingWindow = new BrowserWindow({
      width: 800,
      height: 360,
      frame: false,
      alwaysOnTop: true,
      skipTaskbar: true,
      webPreferences: {
        preload: this.preloadPath,
        webSecurity: false,
        nodeIntegration: true,
      },
      backgroundColor: '#eee',
      show: false,
    });

    // 加载悬浮窗口页面
    if (this.viteDevServerUrl) {
      this.floatingWindow.loadURL(`${this.viteDevServerUrl}#/floating`);
    } else {
      this.floatingWindow.loadURL(`${this.webProdUrl}#/floating`);
    }

    this.floatingWindow.once('ready-to-show', () => {
      this.floatingWindow?.show();
    });

    this.floatingWindow.on('closed', () => {
      this.floatingWindow = null;
    });

    logger.info('悬浮窗口创建完成');
    return this.floatingWindow;
  }

  getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  getFloatingWindow(): BrowserWindow | null {
    return this.floatingWindow;
  }

  showFloatingWindow(): void {
    if (!this.floatingWindow) {
      this.createFloatingWindow();
    } else {
      this.floatingWindow.show();
    }
  }

  hideFloatingWindow(): void {
    if (this.floatingWindow) {
      this.floatingWindow.hide();
    }
  }

  closeFloatingWindow(): void {
    if (this.floatingWindow) {
      this.floatingWindow.close();
      this.floatingWindow = null;
    }
  }

  moveFloatingWindow(deltaX: number, deltaY: number): void {
    if (this.floatingWindow) {
      const [currentX, currentY] = this.floatingWindow.getPosition();
      this.floatingWindow.setPosition(currentX + deltaX, currentY + deltaY);
    }
  }

  minimizeMainWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.minimize();
    }
  }

  closeMainWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.webContents.session.clearStorageData({
        storages: ['localstorage'],
      });
      this.mainWindow.close();
    }
    // 主窗口关闭时，同时关闭悬浮窗口
    this.closeFloatingWindow();
  }

  maximizeMainWindow(maximize?: boolean): void {
    if (!this.mainWindow) return;

    if (maximize !== undefined) {
      if (maximize) {
        this.mainWindow.maximize();
      } else {
        this.mainWindow.unmaximize();
      }
      return;
    }

    if (this.mainWindow.isMaximized()) {
      this.mainWindow.unmaximize();
    } else {
      this.mainWindow.maximize();
    }
  }
}

export default WindowManager;
