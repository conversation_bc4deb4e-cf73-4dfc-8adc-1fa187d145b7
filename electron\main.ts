import { app, BrowserWindow, ipcMain } from 'electron';
import { fileURLToPath } from 'node:url';
import path from 'node:path';
import fs from 'node:fs/promises';
import type { WriteLogParams } from '../shared/electron-api-types';

process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';
const __dirname = path.dirname(fileURLToPath(import.meta.url));

process.env.APP_ROOT = path.join(__dirname, '..');

export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL'];
// TODO: 正式环境时需要换成线上地址
export const WEB_PROD_URL = 'http://************:8950/';
export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron');
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist/electron');

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL
  ? path.join(process.env.APP_ROOT, 'public')
  : RENDERER_DIST;

ipcMain.on('window-minimize', e => {
  const window = BrowserWindow.fromWebContents(e.sender)!;
  window.minimize();
});

ipcMain.on('window-close', async e => {
  const window = BrowserWindow.fromWebContents(e.sender)!;
  await window.webContents.session.clearStorageData({
    storages: ['localstorage'],
  });
  window.close();
});

ipcMain.on('window-maximize', (e, maximize?: boolean) => {
  const window = BrowserWindow.fromWebContents(e.sender)!;
  if (maximize !== undefined) {
    if (maximize) {
      window.maximize();
    } else {
      window.unmaximize();
    }
    return;
  }
  if (window.isMaximized()) {
    window.unmaximize();
  } else {
    window.maximize();
  }
});

// 日志文件管理
class LogFileManager {
  private logDir: string;
  private currentLogFile: string;
  private currentLogSize = 0;

  constructor() {
    // 使用程序根目录
    // this.logDir = path.join(app.getPath('userData'), 'logs');
    this.logDir = './logs';
    this.currentLogFile = path.join(this.logDir, `app-${this.getDateString()}.log`);
    this.ensureLogDir();
  }

  formatTimestamp(): string {
    const timestamp = new Date(Date.now() + 8 * 60 * 60 * 1000)
      .toISOString()
      .replace('T', ' ')
      .replace('Z', '');
    return timestamp;
  }

  private getDateString(): string {
    const now = new Date();
    return now.toISOString().split('T')[0]; // YYYY-MM-DD
  }

  private async ensureLogDir(): Promise<void> {
    try {
      await fs.mkdir(this.logDir, { recursive: true });
    } catch (error) {
      console.error('创建日志目录失败:', error);
    }
  }

  private async getFileSize(filePath: string): Promise<number> {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch {
      return 0;
    }
  }

  private async rotateLogFile(maxFileSize: number, maxFiles: number): Promise<void> {
    try {
      const currentSize = await this.getFileSize(this.currentLogFile);
      if (currentSize >= maxFileSize) {
        // 重命名当前文件
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const rotatedFile = path.join(this.logDir, `app-${this.getDateString()}-${timestamp}.log`);
        await fs.rename(this.currentLogFile, rotatedFile);

        // 清理旧文件
        await this.cleanOldLogFiles(maxFiles);
      }
    } catch (error) {
      console.error('日志文件轮转失败:', error);
    }
  }

  private async cleanOldLogFiles(maxFiles: number): Promise<void> {
    try {
      const files = await fs.readdir(this.logDir);
      const logFiles = files
        .filter(file => file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(this.logDir, file),
        }));

      if (logFiles.length > maxFiles) {
        // 按修改时间排序，删除最旧的文件
        const filesWithStats = await Promise.all(
          logFiles.map(async file => ({
            ...file,
            stats: await fs.stat(file.path),
          })),
        );

        filesWithStats.sort((a, b) => a.stats.mtime.getTime() - b.stats.mtime.getTime());

        const filesToDelete = filesWithStats.slice(0, filesWithStats.length - maxFiles);
        await Promise.all(filesToDelete.map(file => fs.unlink(file.path)));
      }
    } catch (error) {
      console.error('清理旧日志文件失败:', error);
    }
  }

  async writeLog(params: WriteLogParams): Promise<void> {
    try {
      const { message, maxFileSize = 10 * 1024 * 1024, maxFiles = 5 } = params;

      // 检查是否需要轮转日志文件
      await this.rotateLogFile(maxFileSize, maxFiles);

      // 写入日志
      await fs.appendFile(this.currentLogFile, message + '\n', 'utf8');
      this.currentLogSize = await this.getFileSize(this.currentLogFile);
    } catch (error) {
      console.error('写入日志文件失败:', error);
      throw error;
    }
  }
}

let logFileManager: LogFileManager | null = null;

// 处理日志写入请求
ipcMain.handle('write-log', async (event, params: WriteLogParams) => {
  try {
    await logFileManager!.writeLog(params);
    return { success: true };
  } catch (error) {
    console.error('处理日志写入请求失败:', error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
});

// 悬浮窗口相关IPC处理
ipcMain.on('show-floating-window', () => {
  if (!floatingWindow) {
    createFloatingWindow();
  } else {
    floatingWindow.show();
  }
});

ipcMain.on('hide-floating-window', () => {
  if (floatingWindow) {
    floatingWindow.hide();
  }
});

ipcMain.on('close-floating-window', () => {
  if (floatingWindow) {
    floatingWindow.close();
    floatingWindow = null;
  }
});

// 股票代码捕获相关（模拟第三方库调用）
ipcMain.handle('capture-stock-code', async () => {
  // 这里应该调用第三方库来捕获股票代码
  // 目前返回模拟数据
  const mockCodes = ['000001', '000002', '600000', '600036', '300001'];
  const randomCode = mockCodes[Math.floor(Math.random() * mockCodes.length)];
  return randomCode;
});

// 向悬浮窗口发送股票代码
ipcMain.on('send-stock-code-to-floating', (event, stockCode: string) => {
  if (floatingWindow) {
    floatingWindow.webContents.send('stock-code-captured', stockCode);
  }
});

// 移动悬浮窗口
ipcMain.on('move-floating-window', (event, deltaX: number, deltaY: number) => {
  if (floatingWindow) {
    const [currentX, currentY] = floatingWindow.getPosition();
    floatingWindow.setPosition(currentX + deltaX, currentY + deltaY);
  }
});

let mainWindow: BrowserWindow | null;
let floatingWindow: BrowserWindow | null;

function createWindow() {
  mainWindow = new BrowserWindow({
    show: false,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false,
      nodeIntegration: true,
    },
    frame: false,
    width: 1000,
    height: 562.5,
    minWidth: 1000,
    minHeight: 562.5,
    backgroundColor: '#eee',
  });

  if (VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(VITE_DEV_SERVER_URL);
  } else {
    // win.loadFile(path.join(RENDERER_DIST, 'index.html'));
    mainWindow.loadURL(WEB_PROD_URL);
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow?.show();
  });
  mainWindow.on('maximize', () => {
    mainWindow?.webContents.send('window-state-changed', mainWindow.isMaximized());
  });
  mainWindow.on('unmaximize', () => {
    mainWindow?.webContents.send('window-state-changed', mainWindow.isMaximized());
  });
}

function createFloatingWindow() {
  floatingWindow = new BrowserWindow({
    width: 300,
    height: 150,
    frame: false,
    alwaysOnTop: true,
    resizable: false,
    skipTaskbar: true,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false,
      nodeIntegration: true,
    },
    backgroundColor: '#ffffff',
    transparent: false,
    show: false,
  });

  // 加载悬浮窗口页面
  if (VITE_DEV_SERVER_URL) {
    floatingWindow.loadURL(`${VITE_DEV_SERVER_URL}#/floating`);
  } else {
    floatingWindow.loadURL(`${WEB_PROD_URL}#/floating`);
  }

  floatingWindow.once('ready-to-show', () => {
    floatingWindow?.show();
  });

  floatingWindow.on('closed', () => {
    floatingWindow = null;
  });
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  logFileManager?.writeLog({
    message: `[${logFileManager.formatTimestamp()}] [INFO] [ElectronMain] 应用退出`,
  });
  if (process.platform !== 'darwin') {
    app.quit();
    mainWindow = null;
  }
});

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

app.whenReady().then(async () => {
  logFileManager = new LogFileManager();
  createWindow();
  createFloatingWindow();
  // 记录应用启动日志
  await logFileManager.writeLog({
    message: `[${logFileManager.formatTimestamp()}] [INFO] [ElectronMain] 应用启动完成`,
  });
});
