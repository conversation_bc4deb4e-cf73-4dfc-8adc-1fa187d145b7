import { ipcMain } from 'electron';
import type { WriteLogParams } from '../../shared/electron-api-types';
import { LogFileManager } from './log-manager';
import WindowManager from './window-manager';
import StockCaptureService from './stock-capture';
import { createLogger } from '../../src/libs/logger';

const logger = createLogger('IPCHandlers');

export class IPCHandlers {
  private logFileManager: LogFileManager;
  private windowManager: WindowManager;
  private stockCaptureService: StockCaptureService;

  constructor(logFileManager: LogFileManager, windowManager: WindowManager) {
    this.logFileManager = logFileManager;
    this.windowManager = windowManager;
    this.stockCaptureService = StockCaptureService.getInstance();
    this.setupHandlers();
  }

  private setupHandlers(): void {
    this.setupWindowHandlers();
    this.setupLogHandlers();
    this.setupFloatingWindowHandlers();
    this.setupStockCaptureHandlers();
  }

  private setupWindowHandlers(): void {
    // 主窗口控制
    ipcMain.on('window-minimize', () => {
      this.windowManager.minimizeMainWindow();
    });

    ipcMain.on('window-close', () => {
      this.windowManager.closeMainWindow();
    });

    ipcMain.on('window-maximize', (_, maximize?: boolean) => {
      this.windowManager.maximizeMainWindow(maximize);
    });
  }

  private setupLogHandlers(): void {
    // 日志写入处理
    ipcMain.handle('write-log', async (_, params: WriteLogParams) => {
      try {
        await this.logFileManager.writeLog(params);
        return { success: true };
      } catch (error) {
        logger.error('处理日志写入请求失败', error);
        return { 
          success: false, 
          error: error instanceof Error ? error.message : String(error) 
        };
      }
    });
  }

  private setupFloatingWindowHandlers(): void {
    // 悬浮窗口控制
    ipcMain.on('show-floating-window', () => {
      this.windowManager.showFloatingWindow();
    });

    ipcMain.on('hide-floating-window', () => {
      this.windowManager.hideFloatingWindow();
    });

    ipcMain.on('close-floating-window', () => {
      this.windowManager.closeFloatingWindow();
    });

    // 移动悬浮窗口
    ipcMain.on('move-floating-window', (_, deltaX: number, deltaY: number) => {
      this.windowManager.moveFloatingWindow(deltaX, deltaY);
    });
  }

  private setupStockCaptureHandlers(): void {
    // 股票代码捕获相关
    ipcMain.handle('capture-stock-code', async () => {
      try {
        return await this.stockCaptureService.captureStockCode();
      } catch (error) {
        logger.error('捕获股票代码失败', error);
        throw error;
      }
    });

    // 向悬浮窗口发送股票代码
    ipcMain.on('send-stock-code-to-floating', (_, stockCode: string) => {
      const floatingWindow = this.windowManager.getFloatingWindow();
      if (floatingWindow) {
        floatingWindow.webContents.send('stock-code-captured', stockCode);
        logger.info('向悬浮窗口发送股票代码', { stockCode });
      }
    });

    // 启动自动捕获
    ipcMain.on('start-auto-capture', (_, interval?: number) => {
      this.stockCaptureService.startAutoCapture(interval, (stockCode) => {
        const floatingWindow = this.windowManager.getFloatingWindow();
        if (floatingWindow) {
          floatingWindow.webContents.send('stock-code-captured', stockCode);
        }
      });
    });

    // 停止自动捕获
    ipcMain.on('stop-auto-capture', () => {
      this.stockCaptureService.stopAutoCapture();
    });

    // 手动触发捕获
    ipcMain.handle('trigger-capture', async () => {
      try {
        const stockCode = await this.stockCaptureService.triggerCapture();
        const floatingWindow = this.windowManager.getFloatingWindow();
        if (floatingWindow) {
          floatingWindow.webContents.send('stock-code-captured', stockCode);
        }
        return stockCode;
      } catch (error) {
        logger.error('手动触发捕获失败', error);
        throw error;
      }
    });
  }

  /**
   * 清理所有IPC处理器
   */
  cleanup(): void {
    // 移除所有IPC监听器
    ipcMain.removeAllListeners('window-minimize');
    ipcMain.removeAllListeners('window-close');
    ipcMain.removeAllListeners('window-maximize');
    ipcMain.removeAllListeners('show-floating-window');
    ipcMain.removeAllListeners('hide-floating-window');
    ipcMain.removeAllListeners('close-floating-window');
    ipcMain.removeAllListeners('move-floating-window');
    ipcMain.removeAllListeners('send-stock-code-to-floating');
    ipcMain.removeAllListeners('start-auto-capture');
    ipcMain.removeAllListeners('stop-auto-capture');
    
    // 停止股票捕获服务
    this.stockCaptureService.stopAutoCapture();
    
    logger.info('IPC处理器清理完成');
  }
}

export default IPCHandlers;
