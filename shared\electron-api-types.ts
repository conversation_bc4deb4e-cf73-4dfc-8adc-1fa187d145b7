import { ipc<PERSON><PERSON><PERSON> } from 'electron';

export interface Ipc<PERSON>enderer {
  on: typeof ipcRenderer.on;
  emit: typeof ipcRenderer.emit;
  off: typeof ipcRenderer.off;
  send: typeof ipcRenderer.send;
  invoke: typeof ipcRenderer.invoke;
}

// 日志写入参数接口
export interface WriteLogParams {
  message: string;
  maxFileSize?: number;
  maxFiles?: number;
}

// 悬浮窗口相关接口
export interface FloatingWindowAPI {
  onStockCodeCaptured: (callback: (stockCode: string) => void) => void;
  triggerCapture: () => Promise<string>;
}

// 数据同步相关接口
export interface DataSyncAPI {
  setUserLoginStatus: (isLoggedIn: boolean) => void;
  syncPoolsToFloating: (pools: any[]) => void;
  syncPoolDetailsToFloating: (poolDetails: any[]) => void;
  notifyPoolStatusChanged: (poolId: number, status: any) => void;
  notifyPoolDetailStatusChanged: (detailId: number, status: any) => void;
  onPoolsDataUpdated: (callback: (pools: any[]) => void) => void;
  onPoolDetailsDataUpdated: (callback: (poolDetails: any[]) => void) => void;
  onPoolStatusChanged: (callback: (data: { poolId: number; status: any }) => void) => void;
  onPoolDetailStatusChanged: (callback: (data: { detailId: number; status: any }) => void) => void;
}
