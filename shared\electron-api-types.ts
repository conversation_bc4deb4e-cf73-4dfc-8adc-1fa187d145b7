import { ipc<PERSON><PERSON>er } from 'electron';

export interface IpcRenderer {
  on: typeof ipcRenderer.on;
  emit: typeof ipcRenderer.emit;
  off: typeof ipcRenderer.off;
  send: typeof ipcRenderer.send;
  invoke: typeof ipcRenderer.invoke;
}

// 日志写入参数接口
export interface WriteLogParams {
  message: string;
  maxFileSize?: number;
  maxFiles?: number;
}

// 悬浮窗口相关接口
export interface FloatingWindowAPI {
  showFloatingWindow: () => void;
  hideFloatingWindow: () => void;
  closeFloatingWindow: () => void;
  captureStockCode: () => Promise<string>;
  sendStockCodeToFloating: (stockCode: string) => void;
  onStockCodeCaptured: (callback: (stockCode: string) => void) => void;
  moveFloatingWindow: (deltaX: number, deltaY: number) => void;
  startAutoCapture: (interval?: number) => void;
  stopAutoCapture: () => void;
  triggerCapture: () => Promise<string>;
}
