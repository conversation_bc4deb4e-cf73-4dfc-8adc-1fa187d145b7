import { onMounted, onUnmounted } from 'vue';
import PoolService from '@/api/pool';
import { createLogger } from '@/libs/logger';
import type { Pool, PoolDetail } from '@/types';

const logger = createLogger('DataSync');

export function useDataSync() {
  let syncInterval: NodeJS.Timeout | null = null;

  // 同步股票池数据到悬浮窗口
  const syncPoolsData = async () => {
    try {
      const { errorCode, data } = await PoolService.getStrategyPools();
      if (errorCode === 0 && Array.isArray(data)) {
        window.dataSync?.syncPoolsToFloating(data);
        logger.debug('同步股票池数据到悬浮窗口', { poolCount: data.length });
      }
    } catch (error) {
      logger.error('同步股票池数据失败', error);
    }
  };

  // 同步股票池详情数据到悬浮窗口
  const syncPoolDetailsData = async () => {
    try {
      const { errorCode, data } = await PoolService.getAllPoolDetails();
      if (errorCode === 0 && Array.isArray(data)) {
        window.dataSync?.syncPoolDetailsToFloating(data);
        logger.debug('同步股票池详情数据到悬浮窗口', { detailCount: data.length });
      }
    } catch (error) {
      logger.error('同步股票池详情数据失败', error);
    }
  };

  // 同步所有数据
  const syncAllData = async () => {
    await Promise.all([
      syncPoolsData(),
      syncPoolDetailsData()
    ]);
  };

  // 启动定时同步
  const startSync = () => {
    // 立即同步一次
    syncAllData();
    
    // 每5秒同步一次
    syncInterval = setInterval(syncAllData, 5000);
    logger.info('启动数据同步服务');
  };

  // 停止定时同步
  const stopSync = () => {
    if (syncInterval) {
      clearInterval(syncInterval);
      syncInterval = null;
      logger.info('停止数据同步服务');
    }
  };

  // 通知股票池状态变化
  const notifyPoolStatusChanged = (poolId: number, status: any) => {
    window.dataSync?.notifyPoolStatusChanged(poolId, status);
    logger.debug('通知股票池状态变化', { poolId, status });
  };

  // 通知股票池详情状态变化
  const notifyPoolDetailStatusChanged = (detailId: number, status: any) => {
    window.dataSync?.notifyPoolDetailStatusChanged(detailId, status);
    logger.debug('通知股票池详情状态变化', { detailId, status });
  };

  // 生命周期管理
  onMounted(() => {
    // 只在Electron环境下启动同步
    if (window.dataSync) {
      startSync();
    }
  });

  onUnmounted(() => {
    stopSync();
  });

  return {
    syncPoolsData,
    syncPoolDetailsData,
    syncAllData,
    startSync,
    stopSync,
    notifyPoolStatusChanged,
    notifyPoolDetailStatusChanged,
  };
}
