import { ipc<PERSON><PERSON><PERSON>, contextBridge } from 'electron';
import { Ip<PERSON><PERSON><PERSON><PERSON>, FloatingWindowAPI } from '../shared/electron-api-types';

const api: Ipc<PERSON><PERSON>er = {
  on: ipcRenderer.on.bind(ipc<PERSON>enderer),
  emit: ipc<PERSON>enderer.emit.bind(ipc<PERSON><PERSON>er),
  off: ipc<PERSON>enderer.off.bind(ipc<PERSON>enderer),
  send: ipcRenderer.send,
  invoke: ipcRenderer.invoke,
};

const floatingWindowAPI: FloatingWindowAPI = {
  showFloatingWindow: () => ipcRenderer.send('show-floating-window'),
  hideFloatingWindow: () => ipcRenderer.send('hide-floating-window'),
  closeFloatingWindow: () => ipcRenderer.send('close-floating-window'),
  captureStockCode: () => ipcRenderer.invoke('capture-stock-code'),
  sendStockCodeToFloating: (stockCode: string) =>
    ipcRenderer.send('send-stock-code-to-floating', stockCode),
  onStockCodeCaptured: (callback: (stockCode: string) => void) => {
    ipcRenderer.on('stock-code-captured', (_, stockCode: string) => callback(stockCode));
  },
  moveFloatingWindow: (deltaX: number, deltaY: number) =>
    ipcRenderer.send('move-floating-window', deltaX, deltaY),
  startAutoCapture: (interval?: number) => ipcRenderer.send('start-auto-capture', interval),
  stopAutoCapture: () => ipcRenderer.send('stop-auto-capture'),
  triggerCapture: () => ipcRenderer.invoke('trigger-capture'),
};

contextBridge.exposeInMainWorld('ipcRenderer', api);
contextBridge.exposeInMainWorld('floatingWindow', floatingWindowAPI);
