import { ipc<PERSON><PERSON><PERSON>, contextBridge } from 'electron';
import { Ip<PERSON><PERSON><PERSON><PERSON>, FloatingWindowAPI, DataSyncAPI } from '../shared/electron-api-types';

const api: Ipc<PERSON><PERSON>er = {
  on: ipcRenderer.on.bind(ipc<PERSON>enderer),
  emit: ipcRenderer.emit.bind(ipc<PERSON><PERSON><PERSON>),
  off: ipcRenderer.off.bind(ipc<PERSON><PERSON>er),
  send: ipcRenderer.send,
  invoke: ipc<PERSON><PERSON><PERSON>.invoke,
};

const floatingWindowAPI: FloatingWindowAPI = {
  onStockCodeCaptured: (callback: (stockCode: string) => void) => {
    ipcRenderer.on('stock-code-captured', (_, stockCode: string) => callback(stockCode));
  },
  triggerCapture: () => ipcRenderer.invoke('trigger-capture'),
};

const dataSyncAPI: DataSyncAPI = {
  setUserLoginStatus: (isLoggedIn: boolean) =>
    ipcRenderer.send('user-login-status-changed', isLoggedIn),
  syncPoolsToFloating: (pools: any[]) => ipcRenderer.send('sync-pools-to-floating', pools),
  syncPoolDetailsToFloating: (poolDetails: any[]) =>
    ipcRenderer.send('sync-pool-details-to-floating', poolDetails),
  notifyPoolStatusChanged: (poolId: number, status: any) =>
    ipcRenderer.send('pool-status-changed', { poolId, status }),
  notifyPoolDetailStatusChanged: (detailId: number, status: any) =>
    ipcRenderer.send('pool-detail-status-changed', { detailId, status }),
  onPoolsDataUpdated: (callback: (pools: any[]) => void) => {
    ipcRenderer.on('pools-data-updated', (_, pools: any[]) => callback(pools));
  },
  onPoolDetailsDataUpdated: (callback: (poolDetails: any[]) => void) => {
    ipcRenderer.on('pool-details-data-updated', (_, poolDetails: any[]) => callback(poolDetails));
  },
  onPoolStatusChanged: (callback: (data: { poolId: number; status: any }) => void) => {
    ipcRenderer.on('pool-status-changed', (_, data: { poolId: number; status: any }) =>
      callback(data),
    );
  },
  onPoolDetailStatusChanged: (callback: (data: { detailId: number; status: any }) => void) => {
    ipcRenderer.on('pool-detail-status-changed', (_, data: { detailId: number; status: any }) =>
      callback(data),
    );
  },
};

contextBridge.exposeInMainWorld('ipcRenderer', api);
contextBridge.exposeInMainWorld('floatingWindow', floatingWindowAPI);
contextBridge.exposeInMainWorld('dataSync', dataSyncAPI);
