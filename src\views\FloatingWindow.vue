<template>
  <div
    class="floating-window"
    bg="white"
    border="1 solid gray-300"
    rounded="md"
    shadow="lg"
    p="4"
    w="300px"
    h="150px"
  >
    <!-- 标题栏 -->
    <div class="title-bar" flex="~ items-center justify-between" mb="3" @mousedown="startDrag">
      <span text="sm font-medium">股票监控</span>
      <button
        @click="closeWindow"
        @mousedown.stop
        class="close-btn"
        w="6"
        h="6"
        flex="~ items-center justify-center"
        bg="red-500 hover:red-600"
        text="white"
        rounded="full"
        text="xs"
      >
        ×
      </button>
    </div>

    <!-- 股票代码显示区域 -->
    <div class="stock-info" mb="3">
      <div class="stock-code" text="lg font-bold" mb="1">
        {{ currentStockCode || '--' }}
      </div>
      <div class="stock-name" text="sm gray-600">
        {{ currentStockName || '未选择股票' }}
      </div>
    </div>

    <!-- 股票池状态显示 -->
    <div class="pool-status" mb="3">
      <div v-if="stockPools.length > 0" class="pools">
        <div
          v-for="pool in stockPools"
          :key="pool.id"
          class="pool-tag"
          inline-block
          px="2"
          py="1"
          mr="1"
          mb="1"
          rounded="sm"
          text="xs"
          :class="getPoolStatusClass(pool.status)"
        >
          {{ pool.groupName }}
        </div>
      </div>
      <div v-else text="xs gray-500">未在任何股票池中</div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions" flex="~ gap-2">
      <button
        v-if="canOperate"
        @click="startPool"
        :disabled="!hasStoppedPools"
        class="action-btn"
        px="3"
        py="1"
        bg="green-500 hover:green-600 disabled:gray-300"
        text="white xs"
        rounded="sm"
        :class="{ 'cursor-not-allowed': !hasStoppedPools }"
      >
        启动
      </button>
      <button
        v-if="canOperate"
        @click="stopPool"
        :disabled="!hasRunningPools"
        class="action-btn"
        px="3"
        py="1"
        bg="red-500 hover:red-600 disabled:gray-300"
        text="white xs"
        rounded="sm"
        :class="{ 'cursor-not-allowed': !hasRunningPools }"
      >
        停止
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { shallowRef, computed, onMounted, onUnmounted } from 'vue';
import type { Pool } from '@/types';
import { TacticStatusEnum, isStopped } from '@/enum';
import PoolService from '@/api/pool';
import { createLogger } from '@/libs/logger';

const logger = createLogger('FloatingWindow');

// 响应式数据
const currentStockCode = shallowRef<string>('');
const currentStockName = shallowRef<string>('');
const stockPools = shallowRef<Pool[]>([]);
const allPools = shallowRef<Pool[]>([]);

// 计算属性
const canOperate = computed(() => stockPools.value.length > 0);
const hasStoppedPools = computed(() => stockPools.value.some(pool => isStopped(pool)));
const hasRunningPools = computed(() => stockPools.value.some(pool => !isStopped(pool)));

// 获取股票池状态样式类
const getPoolStatusClass = (status: TacticStatusEnum) => {
  switch (status) {
    case TacticStatusEnum.运行中:
      return 'bg-green-100 text-green-800';
    case TacticStatusEnum.已下单:
      return 'bg-blue-100 text-blue-800';
    case TacticStatusEnum.补单监控中:
      return 'bg-yellow-100 text-yellow-800';
    case TacticStatusEnum.已停止:
    case TacticStatusEnum.新建:
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// 窗口拖拽相关
let isDragging = false;
let dragStartX = 0;
let dragStartY = 0;

const startDrag = (e: MouseEvent) => {
  isDragging = true;
  dragStartX = e.clientX;
  dragStartY = e.clientY;

  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);
};

const onDrag = (e: MouseEvent) => {
  if (!isDragging) return;

  const deltaX = e.clientX - dragStartX;
  const deltaY = e.clientY - dragStartY;

  // 通过IPC移动Electron窗口
  window.floatingWindow?.moveFloatingWindow(deltaX, deltaY);

  // 更新拖拽起始位置
  dragStartX = e.clientX;
  dragStartY = e.clientY;
};

const stopDrag = () => {
  isDragging = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 关闭窗口
const closeWindow = () => {
  window.floatingWindow?.hideFloatingWindow();
};

// 启动股票池
const startPool = async () => {
  try {
    const stoppedPools = stockPools.value.filter(pool => isStopped(pool));
    for (const pool of stoppedPools) {
      await PoolService.startStrategyPool(pool.id);
    }
    logger.info('启动股票池成功', { pools: stoppedPools.map(p => p.groupName) });
    await loadStockPools();
  } catch (error) {
    logger.error('启动股票池失败', error);
  }
};

// 停止股票池
const stopPool = async () => {
  try {
    const runningPools = stockPools.value.filter(pool => !isStopped(pool));
    for (const pool of runningPools) {
      await PoolService.stopStrategyPool(pool.id);
    }
    logger.info('停止股票池成功', { pools: runningPools.map(p => p.groupName) });
    await loadStockPools();
  } catch (error) {
    logger.error('停止股票池失败', error);
  }
};

// 加载所有股票池
const loadAllPools = async () => {
  try {
    const pools = await PoolService.getStrategyPools();
    allPools.value = pools;
  } catch (error) {
    logger.error('加载股票池失败', error);
  }
};

// 根据股票代码查找所属股票池
const loadStockPools = async () => {
  if (!currentStockCode.value) {
    stockPools.value = [];
    return;
  }

  try {
    // 获取所有股票池详情，筛选包含当前股票的池
    const allDetails = await PoolService.getAllPoolDetails();
    const stockDetails = allDetails.filter(detail => detail.instrument === currentStockCode.value);

    // 根据股票池ID获取对应的股票池信息
    const poolIds = stockDetails.map(detail => detail.poolId);
    const relatedPools = allPools.value.filter(pool => poolIds.includes(pool.id));

    stockPools.value = relatedPools;
    logger.info('查找股票池完成', {
      stockCode: currentStockCode.value,
      pools: relatedPools.map(p => p.groupName),
    });
  } catch (error) {
    logger.error('查找股票池失败', error);
    stockPools.value = [];
  }
};

// 处理股票代码捕获
const handleStockCodeCaptured = (stockCode: string) => {
  logger.info('捕获到股票代码', { stockCode });
  currentStockCode.value = stockCode;
  // 这里可以通过API获取股票名称
  currentStockName.value = `股票${stockCode}`;
  loadStockPools();
};

// 生命周期
onMounted(async () => {
  await loadAllPools();

  // 监听股票代码捕获事件
  window.floatingWindow?.onStockCodeCaptured(handleStockCodeCaptured);

  // 模拟捕获股票代码（用于测试）
  setTimeout(async () => {
    const mockCode = await window.floatingWindow?.captureStockCode();
    if (mockCode) {
      handleStockCodeCaptured(mockCode);
    }
  }, 2000);
});

onUnmounted(() => {
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
});
</script>

<style scoped>
.floating-window {
  user-select: none;
  position: fixed;
  top: 0;
  left: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.title-bar {
  cursor: move;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: -16px -16px 12px -16px;
  padding: 8px 16px;
  border-radius: 6px 6px 0 0;
}

.close-btn {
  transition: all 0.2s ease;
  font-size: 16px;
  line-height: 1;
}

.close-btn:hover {
  transform: scale(1.1);
}

.action-btn {
  transition: all 0.2s ease;
  font-weight: 500;
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pool-tag {
  transition: all 0.2s ease;
  font-weight: 500;
  border: 1px solid transparent;
}

.stock-code {
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}
</style>
