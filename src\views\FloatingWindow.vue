<script setup lang="ts">
import { shallowRef, computed, onMounted, onUnmounted } from 'vue';
import type { Pool, PoolDetail } from '@/types';
import { isStopped } from '@/enum';
import PoolService from '@/api/pool';
import { createLogger } from '@/libs/logger';

const logger = createLogger('FloatingWindow');

// 响应式数据
const currentStockCode = shallowRef<string>('');
const currentStockName = shallowRef<string>('');
const currentStockInPools = shallowRef<Pool[]>([]);
const allPools = shallowRef<Pool[]>([]);
const currentStockDetail = shallowRef<PoolDetail | null>(null);

// 计算属性
const canOperate = computed(() => currentStockInPools.value.length > 0 && currentStockDetail.value);
const canStart = computed(() => currentStockDetail.value && isStopped(currentStockDetail.value));
const canStop = computed(() => currentStockDetail.value && !isStopped(currentStockDetail.value));

// 获取股票池标签样式类
const getPoolTagClass = (pool: Pool) => {
  const isInPool = currentStockInPools.value.some(p => p.id === pool.id);

  if (isInPool) {
    // 高亮显示当前股票所在的池
    return 'bg-green-200 text-green-900 border-green-400 font-bold';
  } else {
    // 其他池显示为灰色
    return 'bg-gray-100 text-gray-600';
  }
};

// 启动股票自动买入
const startStockAutoBuy = async () => {
  if (!currentStockDetail.value) return;

  try {
    await PoolService.updatePoolDetailStatus(currentStockDetail.value.id, true);
    logger.info('启动股票自动买入成功', {
      stockCode: currentStockCode.value,
      detailId: currentStockDetail.value.id,
    });
    await loadStockPools();
  } catch (error) {
    logger.error('启动股票自动买入失败', error);
  }
};

// 停止股票自动买入
const stopStockAutoBuy = async () => {
  if (!currentStockDetail.value) return;

  try {
    await PoolService.updatePoolDetailStatus(currentStockDetail.value.id, false);
    logger.info('停止股票自动买入成功', {
      stockCode: currentStockCode.value,
      detailId: currentStockDetail.value.id,
    });
    await loadStockPools();
  } catch (error) {
    logger.error('停止股票自动买入失败', error);
  }
};

// 将股票加入股票池
const addStockToPool = async (pool: Pool) => {
  if (!currentStockCode.value) return;

  try {
    await PoolService.addPoolDetail({
      instrument: currentStockCode.value,
      instrumentName: currentStockName.value,
      poolName: pool.groupName,
      poolId: pool.id,
      positionRate: pool.positionRate,
    });

    logger.info('股票加入股票池成功', {
      stockCode: currentStockCode.value,
      poolName: pool.groupName,
    });

    await loadStockPools();
  } catch (error) {
    logger.error('股票加入股票池失败', error);
  }
};

// 加载所有股票池
const loadAllPools = async () => {
  try {
    const { errorCode, errorMsg, data } = await PoolService.getStrategyPools();
    if (errorCode === 0 && Array.isArray(data)) {
      allPools.value = data;
    } else {
      allPools.value = [];
      logger.error('加载股票池失败', { errorCode, errorMsg });
    }
  } catch (error) {
    logger.error('加载股票池失败', error);
  }
};

// 根据股票代码查找所属股票池
const loadStockPools = async () => {
  if (!currentStockCode.value) {
    currentStockInPools.value = [];
    currentStockDetail.value = null;
    return;
  }

  try {
    // 获取所有股票池详情，筛选包含当前股票的池
    const { errorCode, errorMsg, data } = await PoolService.getAllPoolDetails();
    let stockDetails: PoolDetail[] = [];
    if (errorCode === 0 && Array.isArray(data)) {
      stockDetails = data.filter(detail => detail.instrument === currentStockCode.value);
    } else {
      logger.error('加载股票池详情失败', { errorCode, errorMsg });
      return;
    }

    // 根据股票池ID获取对应的股票池信息
    const poolIds = stockDetails.map(detail => detail.poolId);
    const relatedPools = allPools.value.filter(pool => poolIds.includes(pool.id));

    currentStockInPools.value = relatedPools;
    // 获取当前股票的详情（用于启停控制）
    currentStockDetail.value = stockDetails.length > 0 ? stockDetails[0] : null;

    logger.info('查找股票池完成', {
      stockCode: currentStockCode.value,
      pools: relatedPools.map(p => p.groupName),
      stockDetail: currentStockDetail.value?.id,
    });
  } catch (error) {
    logger.error('查找股票池失败', error);
    currentStockInPools.value = [];
    currentStockDetail.value = null;
  }
};

// 处理股票代码捕获
const handleStockCodeCaptured = (stockCode: string) => {
  logger.info('捕获到股票代码', { stockCode });
  currentStockCode.value = stockCode;
  // 这里可以通过API获取股票名称
  currentStockName.value = `股票${stockCode}`;
  loadStockPools();
};

// 生命周期
onMounted(async () => {
  await loadAllPools();

  // 监听股票代码捕获事件
  window.floatingWindow?.onStockCodeCaptured(handleStockCodeCaptured);

  // 模拟捕获股票代码（用于测试）
  setTimeout(async () => {
    const mockCode = await window.floatingWindow?.captureStockCode();
    if (mockCode) {
      handleStockCodeCaptured(mockCode);
    }
  }, 2000);
});
</script>

<template>
  <div class="floating-window">
    <!-- 标题栏 -->
    <div>
      <span>股票监控</span>
    </div>

    <!-- 股票代码显示区域 -->
    <div>
      <div>
        {{ currentStockCode || '--' }}
      </div>
      <div>
        {{ currentStockName || '未选择股票' }}
      </div>
    </div>

    <!-- 股票池状态显示 -->
    <div>
      <div v-if="currentStockInPools.length > 0">
        <div>所在股票池：</div>
        <div v-for="pool in allPools" :key="pool.id" :class="getPoolTagClass(pool)">
          {{ pool.groupName }}
        </div>
      </div>
      <div v-else>
        <div>未在任何股票池中，点击股票池加入：</div>
        <div v-for="pool in allPools" :key="pool.id" @click="addStockToPool(pool)">
          {{ pool.groupName }}
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div>
      <button
        v-if="canOperate"
        @click="startStockAutoBuy"
        :disabled="!canStart"
        :class="{ 'cursor-not-allowed': !canStart }"
      >
        启动自动买入
      </button>
      <button
        v-if="canOperate"
        @click="stopStockAutoBuy"
        :disabled="!canStop"
        :class="{ 'cursor-not-allowed': !canStop }"
      >
        停止自动买入
      </button>
    </div>
  </div>
</template>

<style scoped>
.floating-window {
  -webkit-app-region: drag;
  .controls {
    -webkit-app-region: no-drag;
  }
}
</style>
