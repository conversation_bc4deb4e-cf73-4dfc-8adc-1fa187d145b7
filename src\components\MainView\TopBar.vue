<script setup lang="ts">
import { Misc } from '@/script';
import router from '@/router';
import { LoginService } from '@/api';
import { ws } from '@/api/websocket';
import { createLogger } from '@/libs/logger';

const logger = createLogger('TopBar');

const signOut = () => {
  LoginService.logoutByWS();
  // 停止token自动刷新
  ws.stopTokenAutoRefresh();
  Misc.setUser();
  router.push({ name: 'login' });
};

const start = () => {
  logger.info('全局启动操作');
};

const stop = () => {
  logger.info('全局停止操作');
};

const setting = () => {
  logger.info('打开设置页面');
};

// 测试股票代码捕获
const testStockCapture = async () => {
  try {
    const stockCode = await window.floatingWindow?.triggerCapture();
    if (stockCode) {
      logger.info('测试捕获股票代码', { stockCode });
    }
  } catch (error) {
    logger.error('测试股票代码捕获失败', error);
  }
};
</script>

<template>
  <div px-10 h-40 flex aic jcsb bg="[--g-panel-bg]">
    <div>爱建策略交易系统</div>
    <div>
      <el-button color="var(--g-bg-green-l)" @click="start">
        <i mr-4 fs-14 i-mdi-motion-play-outline />
        全局启动
      </el-button>
      <el-button color="var(--g-red-l)" @click="stop">
        <i mr-4 fs-14 i-mdi-motion-pause-outline />
        全局停止
      </el-button>
      <el-button color="var(--g-primary-l)" @click="testStockCapture">
        <i mr-4 fs-14 i-mdi-test-tube />
        测试捕获
      </el-button>
    </div>
    <div>
      <el-button color="var(--g-primary-l)" @click="setting">
        <i mr-4 fs-14 i-mdi-cog-outline />
        设置
      </el-button>
      <el-button color="var(--g-danger-l)" @click="signOut">
        <i mr-4 fs-14 i-mdi-logout-variant />
        退出
      </el-button>
    </div>
  </div>
</template>

<style scoped></style>
